# AWS Image Builder Component: Configure Windows Update Registry Settings
# This component configures Windows Update registry settings for AWS Systems Manager Patch Manager

name: windows-registry-windowsupdate
description: Configure Windows Update registry settings for AWS Systems Manager Patch Manager compatibility
schemaVersion: 1.0
phases:
- name: build
  steps:
  - name: ConfigureWindowsUpdateSettings
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring Windows Update for Systems Manager Patch Manager..."

        # Create Windows Update registry paths if they don't exist
        $auPath = "HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU"
        $wuPath = "HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate"

        if (-not (Test-Path $auPath)) {
            New-Item -Path $auPath -Force | Out-Null
            Write-Host "Created registry path: $auPath"
        }

        if (-not (Test-Path $wuPath)) {
            New-Item -Path $wuPath -Force | Out-Null
            Write-Host "Created registry path: $wuPath"
        }

        # Disable automatic updates (Systems Manager will handle patching)
        Write-Host "Disabling automatic updates for Systems Manager control..."
        Set-ItemProperty -Path $auPath -Name "NoAutoUpdate" -Value 1 -Type DWord

        # Set to notify for download and notify for install (manual control)
        Write-Host "Setting Windows Update to manual control..."
        Set-ItemProperty -Path $auPath -Name "AUOptions" -Value 2 -Type DWord

        # Ensure no WSUS server is configured (use default Windows Update)
        Write-Host "Removing WSUS server configuration for Systems Manager compatibility..."
        Remove-ItemProperty -Path $wuPath -Name "WUServer" -ErrorAction SilentlyContinue
        Remove-ItemProperty -Path $wuPath -Name "WUStatusServer" -ErrorAction SilentlyContinue
        Remove-ItemProperty -Path $wuPath -Name "UseWUServer" -ErrorAction SilentlyContinue

        # Configure for Systems Manager Patch Manager compatibility
        Write-Host "Configuring for AWS Systems Manager Patch Manager..."
        # Allow Systems Manager to control update installation
        Set-ItemProperty -Path $auPath -Name "ScheduledInstallDay" -Value 0 -Type DWord  # Every day
        Set-ItemProperty -Path $auPath -Name "ScheduledInstallTime" -Value 3 -Type DWord  # 3 AM

- name: validate
  steps:
  - name: ValidateWindowsUpdateConfiguration
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Validating Windows Update registry configuration..."

        $validationErrors = @()

        # Validate Windows Update settings for Systems Manager
        try {
            $noAutoUpdate = Get-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU" -Name "NoAutoUpdate" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty NoAutoUpdate
            if ($noAutoUpdate -ne 1) {
                $validationErrors += "NoAutoUpdate should be 1 (disabled for Systems Manager), found: $noAutoUpdate"
            }

            $auOptions = Get-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU" -Name "AUOptions" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty AUOptions
            if ($auOptions -ne 2) {
                $validationErrors += "AUOptions should be 2 (manual control for Systems Manager), found: $auOptions"
            }

            # Validate WSUS server is NOT configured (should be removed for Systems Manager)
            $wuServer = Get-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate" -Name "WUServer" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty WUServer
            if ($wuServer) {
                $validationErrors += "WUServer should not be configured for Systems Manager, found: $wuServer"
            }
        }
        catch {
            $validationErrors += "Failed to validate Windows Update settings: $($_.Exception.Message)"
        }

        # Report validation results
        if ($validationErrors.Count -eq 0) {
            Write-Host "VALIDATION SUCCESS: Windows Update registry settings configured correctly for Systems Manager"
            exit 0
        } else {
            Write-Error "VALIDATION FAILED: Windows Update registry configuration errors found:"
            foreach ($error in $validationErrors) {
                Write-Error "  - $error"
            }
            exit 1
        }
